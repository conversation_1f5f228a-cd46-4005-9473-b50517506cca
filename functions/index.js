const functions = require("firebase-functions/v2");
const admin = require("firebase-admin");
admin.initializeApp();

exports.sendDailyMedicationReminders = functions.scheduler.onSchedule({
  schedule: "every 1 minutes",
  timeZone: "Asia/Kolkata",
  region: "asia-south1",
}, async (event) => {
  try {
    console.log("Starting medication reminder check...");

    const istNowString = new Date().toLocaleString("en-US", {
      timeZone: "Asia/Kolkata",
    });
    const istNow = new Date(istNowString);

    const currentTime = istNow.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    console.log(`Current IST time: ${currentTime}`);

    const snapshot = await admin.firestore().collection("daily_medication").get();
    console.log(`Found ${snapshot.docs.length} medication documents`);

    let notificationsSent = 0;

    for (const doc of snapshot.docs) {
      const phone = doc.id;
      const data = doc.data();

      if (!data.medicines) {
        console.log(`No medicines found for user: ${phone}`);
        continue;
      }

      for (const med of data.medicines) {
        if (
          med.frequency === "Daily" &&
          med.timing &&
          med.timing.includes(currentTime)
        ) {
          console.log(`Found matching medication for ${phone}: ${med.medicine_name}`);

          const todayIST = new Date(istNowString);
          todayIST.setHours(0, 0, 0, 0);

          const expiry = parseDateIST(med.expiry_date);
          if (expiry < todayIST.getTime()) {
            console.log(`Medication expired for ${phone}: ${med.medicine_name}`);
            continue;
          }

          const userDoc = await admin.firestore().collection("users").doc(phone).get();
          const fcmToken = userDoc.exists ? userDoc.data().fcm_token : null;

          if (!fcmToken) {
            console.log(`No FCM token found for user: ${phone}`);
            continue;
          }

          const message = {
            notification: {
              title: "Medication Reminder",
              body: `Hi, it's time to take ${med.medicine_name}`,
            },
            data: {
              type: "medication_reminder",
              medication_name: med.medicine_name,
              time: currentTime,
              dosage: med.dosage || "",
              phone: phone,
            },
            android: {
              priority: "high",
              notification: {
                channel_id: "medication_reminders",
              },
            },
            token: fcmToken,
          };

          try {
            await admin.messaging().send(message);
            console.log(`Notification sent successfully to ${phone} for ${med.medicine_name}`);
            notificationsSent++;
          } catch (error) {
            console.error(`Failed to send notification to ${phone}:`, error);

            // Handle invalid or expired FCM tokens
            if (error.code === 'messaging/registration-token-not-registered' ||
                error.code === 'messaging/invalid-registration-token') {
              console.log(`Clearing invalid FCM token for user: ${phone}`);
              try {
                // Clear the invalid FCM token from Firestore
                await admin.firestore().collection("users").doc(phone).update({
                  fcm_token: admin.firestore.FieldValue.delete()
                });
                console.log(`Invalid FCM token cleared for user: ${phone}`);
              } catch (clearError) {
                console.error(`Error clearing invalid FCM token for ${phone}:`, clearError);
              }
            }
          }
        }
      }
    }

    console.log(`Medication reminder check completed. Sent ${notificationsSent} notifications.`);
    return { success: true, notificationsSent };

  } catch (error) {
    console.error("Error in medication reminder function:", error);
    throw error;
  }
});

function parseDateIST(dateStr) {
  const [day, month, year] = dateStr.split("/").map((s) => parseInt(s));
  const date = new Date(Date.UTC(year, month - 1, day)); // UTC base
  const istDate = new Date(date.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  istDate.setHours(0, 0, 0, 0);
  return istDate.getTime();
}