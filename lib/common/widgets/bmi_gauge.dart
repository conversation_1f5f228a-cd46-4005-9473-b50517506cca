import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:intl/intl.dart';

class BmiGauge extends ConsumerWidget {
  const BmiGauge({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final latestBMIData = ref.watch(latestBMIProvider);
    final combinedBMI = ref.watch(combinedBmiProvider);

    // Use latest BMI data if available, otherwise fall back to combined BMI
    final bmi = latestBMIData?.bmi ?? combinedBMI;
    final hasLatestData = latestBMIData != null;

    return SfRadialGauge(
      key: const Key('bmiGauge'),
      enableLoadingAnimation: true,
      axes: <RadialAxis>[
        RadialAxis(
          showLabels: false,
          radiusFactor: 0.7,
          minimum: 0,
          maximum: 40,
          showTicks: false,
          axisLineStyle: AxisLineStyle(
            color: Theme.of(context).scaffoldBackgroundColor,
          ),
          ranges: <GaugeRange>[
            GaugeRange(
              startValue: 0,
              endValue: 18.4,
              color: AppColors.gauge1,
              startWidth: 20,
              endWidth: 20,
            ),
            GaugeRange(
              startValue: 18.5,
              endValue: 24.9,
              color: AppColors.gauge2,
              startWidth: 20,
              endWidth: 20,
            ),
            GaugeRange(
              startValue: 25,
              endValue: 29.9,
              color: AppColors.gauge3,
              startWidth: 20,
              endWidth: 20,
            ),
            GaugeRange(
              startValue: 30,
              endValue: 40,
              color: AppColors.gauge4,
              startWidth: 20,
              endWidth: 20,
            ),
          ],
          pointers: <GaugePointer>[
            NeedlePointer(
              value: bmi,
              needleLength: 0.55,
              needleEndWidth: 10,
              needleColor: AppColors.primaryColor,
              knobStyle: KnobStyle(
                borderColor: AppColors.primaryColor,
                borderWidth: 0.02,
                knobRadius: 0.09,
                color: AppColors.white,
              ),
            ),
          ],
          annotations: <GaugeAnnotation>[
            GaugeAnnotation(
              widget: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Space.height(100),
                  Text(
                    bmi.toStringAsFixed(1),
                    style: TextStyle(fontSize: MySize.size24, fontWeight: FontWeight.bold),
                  ),
                 Space.height(4),
                  Text("Your BMI",
                      style:
                          TextStyle(fontSize: MySize.size14, color: AppColors.textGray)),
                  Space.height(4),
                  Text(
                    _bmiCategory(bmi),
                    style: TextStyle(
                      fontSize: MySize.size16,
                      color: _bmiColor(bmi),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                 Space.height(4),
                  Text(
                    _bmiSubtext(bmi),
                    textAlign: TextAlign.center,
                    softWrap: true,
                    maxLines: 4,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      color: AppColors.textGray,
                    ),
                  ),
                  if (hasLatestData) ...[
                    Space.height(8),
                    Text(
                      _formatDate(latestBMIData.date),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: MySize.size10,
                        color: AppColors.textGray,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ]
                ],
              ),
              angle: 90,
              positionFactor: 0.8,
            ),
          ],
        )
      ],
    );
  }

  String _bmiCategory(double bmi) {
    if (bmi < 18.5) return "Underweight";
    if (bmi < 25) return "Normal Weight";
    if (bmi < 30) return "Overweight";
    return "Obese";
  }

  String _bmiSubtext(double bmi) {
    if (bmi < 18.5) {
      return "Your weight is below the healthy range, consider a nutrition plan to gain weight safely.";
    }
    if (bmi < 25) {
      return "You're in a healthy weight range";
    }
    if (bmi < 30) {
      return "Your weight is above the healthy range, consider a weight loss plan";
    }
    return "Consider a weight loss plan for a healthier you";
  }

  Color _bmiColor(double bmi) {
    if (bmi < 18.5) return AppColors.primaryColor;
    if (bmi < 25) return AppColors.primaryColor;
    if (bmi < 30) return AppColors.gauge3;
    return AppColors.gauge4;
  }

  String _formatDate(String dateStr) {
    try {
      // Parse date in format "d-M-yyyy"
      final date = DateFormat('d-M-yyyy').parse(dateStr);
      // Format to "MMM dd, yyyy"
      return DateFormat('MMM dd, yyyy').format(date);
    } catch (e) {
      return dateStr; // Return original if parsing fails
    }
  }
}
