// ignore_for_file: use_build_context_synchronously, curly_braces_in_flow_control_structures

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/thyroid_provider.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import 'package:syncfusion_flutter_charts/charts.dart' as charts;
import 'package:animated_toggle_switch/animated_toggle_switch.dart' as toggle;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

class ThyroidManagementScreen extends ConsumerStatefulWidget {
  const ThyroidManagementScreen({super.key});

  @override
  ConsumerState<ThyroidManagementScreen> createState() =>
      _ThyroidManagementScreenState();
}

class _ThyroidManagementScreenState
    extends ConsumerState<ThyroidManagementScreen> {
  bool switchValue = false;
  String dropdownValue = 'TSH';

  final TextEditingController _tshController = TextEditingController();
  final TextEditingController _t4Controller = TextEditingController();
  final TextEditingController _t3Controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      ref.read(thyroidHistoryProvider.notifier).fetchThyroidHistory();
    });
  }

  @override
  void dispose() {
    _tshController.dispose();
    _t4Controller.dispose();
    _t3Controller.dispose();
    super.dispose();
  }

  void _showAddThyroidValuesDialog(BuildContext context) {
    _tshController.clear();
    _t4Controller.clear();
    _t3Controller.clear();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: Shape.circular(MySize.size16),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(MySize.size24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Enter Thyroid Values",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Space.height(24),
                  Text(
                    "TSH(mIU/L)",
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Space.height(8),
                  TextField(
                    controller: _tshController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: "Enter TSH Value",
                      border: OutlineInputBorder(
                        borderRadius: Shape.circular(MySize.size10),
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: Shape.circular(MySize.size10),
                        borderSide: BorderSide(
                            color: AppColors.primaryColor, width: MySize.size2),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: MySize.size15, vertical: MySize.size15),
                    ),
                  ),
                  Space.height(16),
                  Text(
                    "T4 (ng/dL)",
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Space.height(8),
                  TextField(
                    controller: _t4Controller,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: "Enter T4 Value",
                      border: OutlineInputBorder(
                        borderRadius: Shape.circular(MySize.size10),
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: Shape.circular(MySize.size10),
                        borderSide: BorderSide(
                            color: AppColors.primaryColor, width: MySize.size2),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: MySize.size15, vertical: MySize.size15),
                    ),
                  ),
                  Space.height(16),
                  Text(
                    "T3 (ng/dL)",
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Space.height(8),
                  TextField(
                    controller: _t3Controller,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: "Enter T3 Value",
                      border: OutlineInputBorder(
                        borderRadius: Shape.circular(MySize.size10),
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: Shape.circular(MySize.size10),
                        borderSide: BorderSide(
                            color: AppColors.primaryColor, width: MySize.size2),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: MySize.size15, vertical: MySize.size15),
                    ),
                  ),
                  Space.height(24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: OutlinedButton.styleFrom(
                            padding:
                                EdgeInsets.symmetric(vertical: MySize.size15),
                            side: BorderSide(color: Colors.grey),
                            shape: RoundedRectangleBorder(
                              borderRadius: Shape.circular(MySize.size10),
                            ),
                          ),
                          child: Text(
                            "Cancel",
                            style: TextStyle(
                              color: AppColors.textGray,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      Space.width(16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            final tshText = _tshController.text.trim();
                            final t4Text = _t4Controller.text.trim();
                            final t3Text = _t3Controller.text.trim();

                            if (tshText.isEmpty &&
                                t4Text.isEmpty &&
                                t3Text.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content:
                                      Text("Please enter at least one value"),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            final double? tsh = tshText.isNotEmpty
                                ? double.tryParse(tshText)
                                : null;
                            final double? t4 = t4Text.isNotEmpty
                                ? double.tryParse(t4Text)
                                : null;
                            final double? t3 = t3Text.isNotEmpty
                                ? double.tryParse(t3Text)
                                : null;

                            final Map<String, dynamic> readingData = {
                              'timestamp':
                                  DateTime.now().millisecondsSinceEpoch,
                            };

                            if (tsh != null) readingData['tsh'] = tsh;
                            if (t4 != null) readingData['t4'] = t4;
                            if (t3 != null) readingData['t3'] = t3;

                            final now = DateTime.now();
                            final formattedDate =
                                DateFormat('dd-MM-yyyy').format(now);

                            try {
                              await ref
                                  .read(thyroidHistoryProvider.notifier)
                                  .addThyroidReading(
                                      formattedDate, readingData);
                              Navigator.pop(context);
                            } catch (e) {
                              log("Failed to save thyroid reading: $e");
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            padding:
                                EdgeInsets.symmetric(vertical: MySize.size15),
                            backgroundColor: AppColors.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: Shape.circular(MySize.size10),
                            ),
                          ),
                          child: Text(
                            "Save",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: 'Thyroid Management',
          actions: [
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                _showAddThyroidValuesDialog(context);
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(MySize.size16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                currentTshLevelCard(),
                Space.height(20),
                thyroidIndicatorsRow(),
                Space.height(20),
                Text(
                  "Thyroid Readings",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Space.height(20),
                ThyroidToggleSwitch(
                  switchValue: switchValue,
                  onToggleChanged: (value) {
                    setState(() {
                      switchValue = value;
                    });
                  },
                ),
                Space.height(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Thyroid Levels",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: MySize.size8),
                      child: DropdownButton<String>(
                        value: dropdownValue,
                        borderRadius: Shape.circular(MySize.size12),
                        style: TextStyle(
                            color: AppColors.textGray, fontSize: MySize.size14),
                        dropdownColor: Theme.of(context).cardColor,
                        underline: Container(),
                        items: const [
                          DropdownMenuItem(
                            value: 'TSH',
                            child: Text('TSH'),
                          ),
                          DropdownMenuItem(
                            value: 'T3',
                            child: Text('T3'),
                          ),
                          DropdownMenuItem(
                            value: 'T4',
                            child: Text('T4'),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              dropdownValue = value;
                            });
                          }
                        },
                      ),
                    )
                  ],
                ),
                Space.height(20),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: Shape.circular(MySize.size24),
                    color: Theme.of(context).cardColor,
                  ),
                  width: double.infinity,
                  height: MediaQuery.of(context).size.height / 2.4,
                  child: Padding(
                    padding: EdgeInsets.all(MySize.size20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Space.height(10),
                        Expanded(
                          child: ThyroidFunctionChart(
                            isMonthly: switchValue,
                            chartType: dropdownValue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Space.height(20),
                Text(
                  "Recommendations",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Space.height(20),
                thyroidRecommendations(context),
              ],
            ),
          ),
        ));
  }
}

// Widget for the current TSH level card
Widget currentTshLevelCard() {
  return Consumer(builder: (context, ref, child) {
    final latestReading = ref.watch(latestThyroidReadingProvider);
    final thyroidStatus = ref.watch(thyroidStatusProvider);

    double tshValue = 0.0;
    String statusText = 'No thyroid data available';
    Color statusColor = Colors.grey;

    if (latestReading != null && latestReading['tsh'] != null) {
      tshValue = latestReading['tsh'];

      switch (thyroidStatus) {
        case 'Hyperthyroidism':
          statusText = 'Your thyroid is overactive';
          statusColor = Colors.orange;
          break;
        case 'Normal':
          statusText = 'Your thyroid function is within normal range';
          statusColor = AppColors.primaryColor;
          break;
        case 'Hypothyroidism':
          statusText = 'Your thyroid is underactive';
          statusColor = Colors.red;
          break;
        default:
          statusText = 'Thyroid status unknown';
          statusColor = Colors.grey;
      }
    }

    return Card(
      elevation: 0,
      color: Theme.of(context).cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: Shape.circular(MySize.size12),
      ),
      child: Padding(
        padding: EdgeInsets.all(MySize.size16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current TSH Level',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Space.height(8),
            Center(
              child: SizedBox(
                height: MySize.size220,
                child: tshGauge(tshValue: tshValue),
              ),
            ),
            Center(
              child: Text(
                statusText,
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: statusColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  });
}

// Widget for the circular gauge showing TSH level
Widget tshGauge({required double tshValue}) {
  return SfRadialGauge(
    enableLoadingAnimation: true,
    axes: <RadialAxis>[
      RadialAxis(
        minimum: 0,
        maximum: 10,
        showLabels: false,
        showTicks: false,
        startAngle: 270,
        endAngle: 270,
        radiusFactor: 0.8,
        axisLineStyle: AxisLineStyle(
          thicknessUnit: GaugeSizeUnit.factor,
          thickness: 0.15, // Using factor unit, not MySize
          color: Color(0xFFE0E0E0),
        ),
        pointers: <GaugePointer>[
          RangePointer(
            value: tshValue,
            cornerStyle: CornerStyle.bothCurve,
            enableAnimation: true,
            animationDuration: 1200,
            sizeUnit: GaugeSizeUnit.factor,
            color: AppColors.primaryColor,
            width: 0.15, // Using factor unit, not MySize
          ),
        ],
        annotations: <GaugeAnnotation>[
          GaugeAnnotation(
            angle: 270,
            widget: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  tshValue.toString(),
                  style: TextStyle(
                    fontSize: MySize.size36,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Tsh mU/L',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ],
  );
}

// Widget for the row of thyroid indicators
Widget thyroidIndicatorsRow() {
  return Consumer(builder: (context, ref, child) {
    final latestReading = ref.watch(latestThyroidReadingProvider);

    String tshValue = 'N/A';
    String t3Value = 'N/A';
    String t4Value = 'N/A';

    if (latestReading != null) {
      if (latestReading['tsh'] != null) {
        tshValue = latestReading['tsh'].toStringAsFixed(1);
      }

      if (latestReading['t3'] != null) {
        t3Value = latestReading['t3'].toStringAsFixed(1);
      }

      if (latestReading['t4'] != null) {
        t4Value = latestReading['t4'].toStringAsFixed(1);
      }
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        thyroidIndicatorCard(
          title: 'Tsh',
          value: tshValue,
          range: '0.4-4.0',
          context: context,
        ),
        thyroidIndicatorCard(
          title: 'T3',
          value: t3Value,
          range: '2.3-4.2',
          context: context,
        ),
        thyroidIndicatorCard(
          title: 'T4',
          value: t4Value,
          range: '0.8-1.8',
          context: context,
        ),
      ],
    );
  });
}

// Widget for individual thyroid indicator cards
Widget thyroidIndicatorCard({
  required String title,
  required String value,
  required String range,
  required BuildContext context,
}) {
  return Card(
    elevation: 0,
    color: Theme.of(context).cardColor,
    shape: RoundedRectangleBorder(
      borderRadius: Shape.circular(MySize.size8),
    ),
    child: Padding(
      padding: EdgeInsets.symmetric(
          vertical: MySize.size16, horizontal: MySize.size35),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textGray,
                fontWeight: FontWeight.w800),
          ),
          Space.height(8),
          Text(
            value,
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Space.height(4),
          Text(
            range,
            style: TextStyle(
              fontSize: MySize.size10,
              color: AppColors.textGray,
            ),
          ),
        ],
      ),
    ),
  );
}

// Widget for the weekly/monthly toggle switch
class ThyroidToggleSwitch extends StatelessWidget {
  final bool switchValue;
  final Function(bool) onToggleChanged;

  const ThyroidToggleSwitch({
    super.key,
    required this.switchValue,
    required this.onToggleChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: toggle.AnimatedToggleSwitch<bool>.size(
        current: switchValue,
        values: const [false, true],
        iconOpacity: 0.2,
        inactiveOpacity: 1.0,
        indicatorSize: const Size.fromWidth(120),
        customIconBuilder: (context, local, global) => Text(
          local.value ? 'Monthly' : 'Weekly',
          style: TextStyle(
            fontSize: MySize.size15,
            fontWeight: FontWeight.w700,
            color: Color.lerp(Theme.of(context).textTheme.bodySmall?.color,
                AppColors.backgroundColor, local.animationValue),
          ),
        ),
        borderWidth: 1,
        iconAnimationType: toggle.AnimationType.onHover,
        style: toggle.ToggleStyle(
          borderColor: AppColors.textGray,
          indicatorColor: AppColors.primaryColor,
          borderRadius: Shape.circular(MySize.size30),
          indicatorBorderRadius: Shape.circular(MySize.size20),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        ),
        selectedIconScale: 1,
        onChanged: onToggleChanged,
      ),
    );
  }
}

// Widget for the thyroid function chart
class ThyroidFunctionChart extends ConsumerWidget {
  final bool isMonthly;
  final String chartType;

  const ThyroidFunctionChart({
    super.key,
    required this.isMonthly,
    required this.chartType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get data from provider
    final thyroidData = ref.watch(thyroidHistoryProvider);
    final List<ChartData> chartData = _generateChartData(ref, thyroidData);

    return charts.SfCartesianChart(
      onDataLabelRender: (charts.DataLabelRenderArgs args) {
        args.textStyle = TextStyle(
            fontSize: MySize.size10,
            color: Theme.of(context).textTheme.bodySmall?.color);
      },
      zoomPanBehavior: charts.ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
      ),
      primaryXAxis: charts.CategoryAxis(
        // Set initial visible range to show the most recent data points
        initialVisibleMinimum: chartData.length > 7 ? chartData.length - 7 : 0,
        labelRotation: -45,
        labelStyle: TextStyle(
            fontSize: MySize.size10,
            color: Theme.of(context).textTheme.bodySmall?.color),
        labelIntersectAction: charts.AxisLabelIntersectAction.wrap,
        // Format the date labels to be more concise
        axisLabelFormatter: (charts.AxisLabelRenderDetails details) {
          final parts = details.text.split(' ');
          return charts.ChartAxisLabel(parts.first, details.textStyle);
        },
      ),
      primaryYAxis: charts.NumericAxis(
        minimum: 0,
        maximum: _calculateYAxisMax(chartData),
        interval: _calculateYAxisInterval(_calculateYAxisMax(chartData)),
        labelStyle: TextStyle(
            fontSize: MySize.size10,
            color: Theme.of(context).textTheme.bodySmall?.color),
      ),
      tooltipBehavior: charts.TooltipBehavior(enable: true),
      series: <charts.CartesianSeries<dynamic, dynamic>>[
        charts.LineSeries<ChartData, String>(
          dataSource: chartData,
          xValueMapper: (ChartData data, _) => data.date,
          yValueMapper: (ChartData data, _) => data.value,
          dataLabelSettings: charts.DataLabelSettings(isVisible: true),
          markerSettings: charts.MarkerSettings(isVisible: true),
          name: chartType,
          color: AppColors.primaryColor,
        )
      ],
    );
  }

  // Generate data for the chart from thyroid history
  List<ChartData> _generateChartData(
      WidgetRef ref, Map<String, Map<String, dynamic>> thyroidData) {
    final List<ChartData> chartData = [];

    if (thyroidData.isEmpty) {
      // Return empty data if no readings are available
      return chartData;
    }

    // Helper function to parse date strings
    DateTime parseDate(String dateStr) {
      final parts = dateStr.split('-');
      if (parts.length == 3) {
        return DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
      }
      return DateTime.now();
    }

    // Sort dates
    final sortedDates = thyroidData.keys.toList()
      ..sort((a, b) => parseDate(a).compareTo(parseDate(b)));

    // For weekly view, show all readings with detailed dates
    if (!isMonthly) {
      // Use all dates for more data points
      for (final date in sortedDates) {
        final readings = thyroidData[date]?['readings'] as List<dynamic>?;
        if (readings != null && readings.isNotEmpty) {
          for (final reading in readings) {
            final Map<String, dynamic> readingMap =
                reading as Map<String, dynamic>;

            // Get the value based on selected chart type
            final value = readingMap[chartType.toLowerCase()] as double?;
            if (value != null) {
              // Format date as DD/MM
              final dateObj = parseDate(date);
              final timestamp = readingMap['timestamp'] as int?;
              String displayDate;

              if (timestamp != null) {
                // If we have a timestamp, include time
                final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
                displayDate =
                    "${DateFormat('dd/MM').format(dateObj)} ${DateFormat('HH:mm').format(dateTime)}";
              } else {
                // Otherwise just use the date
                displayDate = DateFormat('dd/MM').format(dateObj);
              }

              chartData.add(ChartData(displayDate, value));
            }
          }
        }
      }
    }
    // For monthly view, show data by month
    else {
      // Group data by month
      final Map<String, List<double>> monthlyData = {};

      for (final date in sortedDates) {
        final dateObj = parseDate(date);
        final monthKey = DateFormat('MM/yy').format(dateObj);

        final readings = thyroidData[date]?['readings'] as List<dynamic>?;
        if (readings != null && readings.isNotEmpty) {
          for (final reading in readings) {
            final Map<String, dynamic> readingMap =
                reading as Map<String, dynamic>;

            // Get the value based on selected chart type
            final value = readingMap[chartType.toLowerCase()] as double?;
            if (value != null) {
              if (!monthlyData.containsKey(monthKey)) {
                monthlyData[monthKey] = [];
              }
              monthlyData[monthKey]!.add(value);
            }
          }
        }
      }

      // Calculate monthly averages
      monthlyData.forEach((month, values) {
        if (values.isNotEmpty) {
          final average = values.reduce((a, b) => a + b) / values.length;
          chartData.add(ChartData(month, average));
        }
      });

      // Sort by month
      chartData.sort((a, b) {
        try {
          final aDate = DateFormat('MM/yy').parse(a.date);
          final bDate = DateFormat('MM/yy').parse(b.date);
          return aDate.compareTo(bDate);
        } catch (e) {
          return 0;
        }
      });
    }
    // If no data is available, return sample data
    if (chartData.isEmpty) {
      return _getSampleData();
    }

    return chartData;
  }

  // Provide sample data when no real data is available
  List<ChartData> _getSampleData() {
    if (isMonthly) {
      // Monthly sample data
      return [
        ChartData('01/23', 2.1),
        ChartData('02/23', 2.3),
        ChartData('03/23', 1.9),
        ChartData('04/23', 2.2),
        ChartData('05/23', 2.0),
        ChartData('06/23', 2.1),
      ];
    } else {
      // Weekly sample data with DD/MM format
      final now = DateTime.now();
      final formatter = DateFormat('dd/MM');

      return [
        ChartData(formatter.format(now.subtract(const Duration(days: 6))), 2.0),
        ChartData(formatter.format(now.subtract(const Duration(days: 5))), 2.1),
        ChartData(formatter.format(now.subtract(const Duration(days: 4))), 1.9),
        ChartData(formatter.format(now.subtract(const Duration(days: 3))), 2.2),
        ChartData(formatter.format(now.subtract(const Duration(days: 2))), 2.3),
        ChartData(formatter.format(now.subtract(const Duration(days: 1))), 2.1),
        ChartData(formatter.format(now), 2.0),
      ];
    }
  }

  double _calculateYAxisMax(List<ChartData> chartData) {
    if (chartData.isEmpty) return 5.0;

    final maxValue =
        chartData.map((e) => e.value).reduce((a, b) => a > b ? a : b);
    double buffer = 0.2;

    switch (chartType) {
      case 'TSH':
        buffer = 0.5;
        break;
      case 'T3':
        buffer = 20.0;
        break;
      case 'T4':
        buffer = 0.2;
        break;
    }

    return (maxValue + buffer).ceilToDouble();
  }

  double _calculateYAxisInterval(double yAxisMax) {
    switch (chartType) {
      case 'TSH':
        return yAxisMax <= 5 ? 0.5 : 1.0;
      case 'T3':
        if (yAxisMax <= 100) {
          return 10.0;
        } else if (yAxisMax <= 200)
          return 20.0;
        else
          return 25.0;
      case 'T4':
        return yAxisMax <= 3 ? 0.2 : 0.5;
      default:
        return 0.5;
    }
  }
}

// Data class for chart points
class ChartData {
  final String date;
  final double value;

  ChartData(this.date, this.value);
}

// Widget for thyroid recommendations
Widget thyroidRecommendations(BuildContext context) {
  return Column(
    children: [
      recommendationItem(
        context: context,
        icon: SvgPicture.asset(
          'assets/svg/meal_icon.svg',
          colorFilter:
              ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
        ),
        title: 'Eat a Balanced Diet',
        description: 'Include iodine-rich foods(fish,dairy)',
      ),
      Space.height(8),
      recommendationItem(
        context: context,
        icon: SvgPicture.asset(
          'assets/svg/walking_icon.svg',
          colorFilter:
              ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
        ),
        title: 'Regular Exercise',
        description: 'Moderate activity supports metabolism',
      ),
      Space.height(8),
      recommendationItem(
        context: context,
        icon: SvgPicture.asset(
          'assets/svg/sleep_icon.svg',
          colorFilter:
              ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
        ),
        title: 'Get Enough Sleep',
        description: 'Aim for 7-8 hours of sleep daily',
      ),
      Space.height(8),
      recommendationItem(
        context: context,
        icon: SvgPicture.asset(
          'assets/svg/medication_icon.svg',
          colorFilter:
              ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
        ),
        title: 'Take Medications on Time',
        description: 'If prescribed, take thyroid meds as directed',
      ),
    ],
  );
}

// Widget for individual recommendation items
Widget recommendationItem({
  required Widget icon,
  required String title,
  required String description,
  required BuildContext context,
}) {
  return Card(
    elevation: 0,
    color: Theme.of(context).cardColor,
    shape: RoundedRectangleBorder(
      borderRadius: Shape.circular(MySize.size12),
    ),
    child: Padding(
      padding: EdgeInsets.all(MySize.size16),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: Shape.circular(MySize.size8),
            ),
            child: icon,
          ),
          Space.width(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Space.height(4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textGray,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
