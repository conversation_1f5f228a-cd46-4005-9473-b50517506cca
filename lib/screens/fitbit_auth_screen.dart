import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import '../models/fitbit_models.dart';
import '../providers/fitbit_provider.dart';

class FitBitAuthScreen extends ConsumerStatefulWidget {
  const FitBitAuthScreen({super.key});

  @override
  ConsumerState<FitBitAuthScreen> createState() => _FitBitAuthScreenState();
}

class _FitBitAuthScreenState extends ConsumerState<FitBitAuthScreen> {
  @override
  Widget build(BuildContext context) {
    final fitbitState = ref.watch(fitbitProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(title: "FitBit Integration"),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(MySize.size20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // FitBit Logo
            Container(
              width: MySize.size120,
              height: MySize.size120,
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(MySize.size20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: MySize.size10,
                    offset: Offset(0, MySize.size4),
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  Icons.fitness_center,
                  size: MySize.size60,
                  color: AppColors.primaryColor,
                ),
              ),
            ),

            Space.height(MySize.size30),

            // Title
            Text(
              'Connect Your FitBit',
              style: TextStyle(
                fontSize: MySize.size24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
              textAlign: TextAlign.center,
            ),

            Space.height(MySize.size16),

            // Description
            Text(
              'Sync your FitBit data to get comprehensive health insights including steps, heart rate, sleep, and more.',
              style: TextStyle(
                fontSize: MySize.size16,
                color: AppColors.textGray,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            Space.height(MySize.size40),

            // Connection Status
            _buildConnectionStatus(fitbitState),

            Space.height(MySize.size30),

            // Features List
            _buildFeaturesList(),

            Space.height(MySize.size30),

            // Connect/Disconnect Button
            _buildActionButton(fitbitState),

            if (fitbitState.hasError) ...[
              Space.height(MySize.size20),
              _buildErrorMessage(fitbitState.error!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus(FitBitState state) {
    IconData icon;
    String title;
    String subtitle;
    Color color;

    switch (state.connectionStatus) {
      case FitBitConnectionStatus.connected:
        icon = Icons.check_circle;
        title = 'Connected';
        subtitle = state.user?.displayName ?? 'FitBit account connected';
        color = AppColors.success;
        break;
      case FitBitConnectionStatus.connecting:
        icon = Icons.sync;
        title = 'Connecting...';
        subtitle = 'Please complete authentication in browser';
        color = AppColors.warning;
        break;
      case FitBitConnectionStatus.tokenExpired:
        icon = Icons.warning;
        title = 'Connection Expired';
        subtitle = 'Please reconnect your FitBit account';
        color = AppColors.warning;
        break;
      case FitBitConnectionStatus.error:
        icon = Icons.error;
        title = 'Connection Error';
        subtitle = 'Failed to connect to FitBit';
        color = AppColors.error;
        break;
      default:
        icon = Icons.link_off;
        title = 'Not Connected';
        subtitle = 'Connect your FitBit to sync health data';
        color = AppColors.textSecondary;
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: MySize.size48,
            height: MySize.size48,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(MySize.size24),
            ),
            child: Icon(
              icon,
              color: color,
              size: MySize.size24,
            ),
          ),
          Space.width(MySize.size16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
                Space.height(MySize.size4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textGray,
                  ),
                ),
              ],
            ),
          ),
          if (state.isLoading)
            SizedBox(
              width: MySize.size20,
              height: MySize.size20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      {
        'icon': Icons.directions_walk,
        'title': 'Steps & Distance',
        'subtitle': 'Track your daily activity and movement',
      },
      {
        'icon': Icons.favorite,
        'title': 'Heart Rate',
        'subtitle': 'Monitor your heart rate throughout the day',
      },
      {
        'icon': Icons.bedtime,
        'title': 'Sleep Analysis',
        'subtitle': 'Detailed sleep stages and quality metrics',
      },
      {
        'icon': Icons.local_fire_department,
        'title': 'Calories Burned',
        'subtitle': 'Track your energy expenditure',
      },
    ];

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What you\'ll get:',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          Space.height(MySize.size16),
          ...features.map((feature) => Padding(
            padding: EdgeInsets.only(bottom: MySize.size12),
            child: Row(
              children: [
                Container(
                  width: MySize.size40,
                  height: MySize.size40,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(MySize.size20),
                  ),
                  child: Icon(
                    feature['icon'] as IconData,
                    color: AppColors.primaryColor,
                    size: MySize.size20,
                  ),
                ),
                Space.width(MySize.size12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feature['title'] as String,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                      Text(
                        feature['subtitle'] as String,
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textGray,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildActionButton(FitBitState state) {
    String buttonText;
    VoidCallback? onPressed;
    Color buttonColor = AppColors.primaryColor;

    switch (state.connectionStatus) {
      case FitBitConnectionStatus.connected:
        buttonText = 'Disconnect FitBit';
        buttonColor = AppColors.error;
        onPressed = state.isLoading ? null : () => _showDisconnectDialog();
        break;
      case FitBitConnectionStatus.connecting:
        buttonText = 'Connecting...';
        onPressed = null;
        break;
      case FitBitConnectionStatus.tokenExpired:
      case FitBitConnectionStatus.error:
      case FitBitConnectionStatus.disconnected:
      buttonText = 'Connect FitBit';
        onPressed = state.isLoading ? null : () => _connectFitBit();
    }

    return SizedBox(
      width: double.infinity,
      height: MySize.size50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size12),
          ),
          elevation: 0,
        ),
        child: state.isLoading
            ? SizedBox(
                width: MySize.size20,
                height: MySize.size20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                buttonText,
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MySize.size8),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: MySize.size20,
          ),
          Space.width(MySize.size12),
          Expanded(
            child: Text(
              error,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.error,
              ),
            ),
          ),
          IconButton(
            onPressed: () => ref.read(fitbitProvider.notifier).clearError(),
            icon: Icon(
              Icons.close,
              color: AppColors.error,
              size: MySize.size18,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _connectFitBit() async {
    try {
      final success = await ref.read(fitbitProvider.notifier).authenticate();

      if (success) {
        // Show success message or navigate
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please complete authentication in your browser'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to start FitBit authentication'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _showDisconnectDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text(
          'Disconnect FitBit',
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
        content: Text(
          'Are you sure you want to disconnect your FitBit account? This will stop syncing your health data.',
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(foregroundColor: AppColors.primaryColor),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: AppColors.red),
            child: Text('Disconnect'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(fitbitProvider.notifier).disconnect();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('FitBit disconnected successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }
}
