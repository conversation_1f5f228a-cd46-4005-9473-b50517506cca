import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/animated_button.dart';
import 'package:healo/common/widgets/bmi_recommendation_cards.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/bmi_gauge.dart';
import 'package:healo/common/widgets/custom_bmi_chart.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:healo/common/widgets/bmi_category_cards.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:healo/route/route_constants.dart';

class BmiScreen extends ConsumerStatefulWidget {
  const BmiScreen({super.key});
  @override
  ConsumerState<BmiScreen> createState() => _BmiScreenState();
}

class _BmiScreenState extends ConsumerState<BmiScreen> {
  bool switchValue = false;

  void _showEditMeasurementsDialog(
      BuildContext context, WidgetRef ref, double height, double weight) {
    final heightController = TextEditingController(text: height.toString());
    final weightController = TextEditingController(text: weight.toString());
    String heightUnit = 'cm';
    String weightUnit = 'kg';

    void closeDialog() {
      Navigator.of(context).pop();
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size20)),
          titlePadding: EdgeInsets.only(
              top: MySize.size16, left: MySize.size16, right: MySize.size16),
          contentPadding: EdgeInsets.symmetric(horizontal: MySize.size16),
          actionsPadding: EdgeInsets.only(bottom: MySize.size16),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Edit Measurements',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.bodyMedium!.color,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Height',
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.textGray,
                  )),
              Space.height(5),
              _buildNumberFieldWithUnit(
                controller: heightController,
                unit: heightUnit,
              ),
              Space.height(20),
              Text('Weight',
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.textGray,
                  )),
              Space.height(5),
              _buildNumberFieldWithUnit(
                controller: weightController,
                unit: weightUnit,
              ),
              Space.height(20)
            ],
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: [
            CustomAsyncButton(
              text: 'Save',
              isDialog: true,
              onPressed: () async {
                final double? newHeight =
                    double.tryParse(heightController.text);
                final double? newWeight =
                    double.tryParse(weightController.text);

                if (newHeight != null && newWeight != null) {
                  // Save measurements to BMI collection
                  await ref
                      .read(measurementsProvider.notifier)
                      .saveMeasurements(
                        height: newHeight,
                        heightUnit: heightUnit,
                        weight: newWeight,
                        weightUnit: weightUnit,
                      );

                  // Wait for Firestore to propagate the data
                  await Future.delayed(Duration(milliseconds: 500));

                  // Invalidate user data providers to update the combined providers
                  ref.invalidate(userDataProvider);
                  ref.invalidate(userDataStreamProvider);
                  ref.invalidate(userWeightProvider);
                  ref.invalidate(userHeightProvider);
                  ref.invalidate(combinedBmiProvider);

                  // Refresh all BMI-related data
                  await _refreshAllBMIData();

                  // Show success message
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Measurements updated successfully!'),
                        backgroundColor: AppColors.primaryColor,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }

                  // Small delay to ensure UI updates
                  await Future.delayed(Duration(milliseconds: 300));
                }

                closeDialog();

                return false;
              },
              color: AppColors.primaryColor,
            ),
          ],
        );
      },
    );
  }

  Widget _buildNumberFieldWithUnit({
    required TextEditingController controller,
    required String unit,
  }) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: TextFormField(
            controller: controller,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              suffixIcon: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      double newValue =
                          (double.tryParse(controller.text) ?? 0) + 1;
                      controller.text = newValue.toStringAsFixed(1);
                    },
                    child: const Icon(Icons.arrow_drop_up),
                  ),
                  GestureDetector(
                    onTap: () {
                      double newValue =
                          (double.tryParse(controller.text) ?? 0) - 1;
                      controller.text = newValue.toStringAsFixed(1);
                    },
                    child: const Icon(Icons.arrow_drop_down),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              unit,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    _refreshAllBMIData();
  }

  Future<void> _refreshAllBMIData() async {
    await Future.wait([
      ref.read(bmiHistoryProvider.notifier).fetchBMIHistory(),
      ref.read(bmiMonthlyHistoryProvider.notifier).fetchBMIMonthlyHistory(),
      ref.read(bmiProvider.notifier).fetchBMI(),
      ref.read(latestBMIProvider.notifier).fetchLatestBMI(),
      ref.read(heightProvider.notifier).fetchHeight(),
      ref.read(weightProvider.notifier).fetchWeight(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final Map<String, Map<String, dynamic>> bmiHistory =
        ref.watch(bmiHistoryProvider);
    final Map<String, Map<String, dynamic>> bmiMonthlyHistory =
        ref.watch(bmiMonthlyHistoryProvider);
    final double height = ref.watch(heightProvider);
    final double weight = ref.watch(weightProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Your BMI Details",
        actions: [
          PopupMenuButton<String>(
            borderRadius: BorderRadius.circular(MySize.size14),
            color: Theme.of(context).cardColor,
            icon: const Icon(Icons.more_vert),
            onSelected: (String value) {
              if (value == 'edit') {
                _showEditMeasurementsDialog(context, ref, height, weight);
              } else if (value == 'history') {
                Navigator.pushNamed(context, bmiHistoryScreen);
              }
            },
            itemBuilder: (BuildContext context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/edit_measurements_icon.svg",
                      width: MySize.size20,
                      height: MySize.size20,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).textTheme.bodyMedium!.color!,
                        BlendMode.srcIn,
                      ),
                    ),
                    Space.width(10),
                    Text("Edit Measurements",
                        style: TextStyle(
                            color:
                                Theme.of(context).textTheme.bodyMedium?.color)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'history',
                child: Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/history_icon.svg",
                      width: MySize.size20,
                      height: MySize.size20,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).textTheme.bodyMedium!.color!,
                        BlendMode.srcIn,
                      ),
                    ),
                    Space.width(10),
                    Text("History",
                        style: TextStyle(
                            color:
                                Theme.of(context).textTheme.bodyMedium?.color)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            left: MySize.size15,
            right: MySize.size15,
            bottom: MySize.size15,
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: MySize.size30,
                  right: MySize.size30,
                ),
                child: Center(
                  child: BmiGauge(),
                ),
              ),
              Space.height(40),
              // Weight and Height Cards
              Consumer(
                builder: (context, ref, child) {
                  // Watch all relevant providers
                  final latestBMIData = ref.watch(latestBMIProvider);
                  final userHeight = ref.watch(userHeightProvider);
                  final userWeight = ref.watch(userWeightProvider);
                  final heightFromProvider = ref.watch(heightProvider);
                  final weightFromProvider = ref.watch(weightProvider);

                  // Use latest BMI data if available, otherwise fall back to user profile data or provider data
                  final height = latestBMIData?.height ??
                                userHeight ??
                                (heightFromProvider > 0 ? heightFromProvider : 0.0);
                  final weight = latestBMIData?.weight ??
                                userWeight ??
                                (weightFromProvider > 0 ? weightFromProvider : 0.0);
                  final heightUnit = latestBMIData?.heightUnit ?? 'cm';
                  final weightUnit = latestBMIData?.weightUnit ?? 'kg';

                  return Row(
                    children: [
                      Expanded(
                        child: _buildBMICard(
                          iconAsset: "assets/svg/height_icon.svg",
                          title: "Height",
                          value: height > 0 ? height.toStringAsFixed(0) : "00",
                          unit: heightUnit,
                        ),
                      ),
                      Space.width(15),
                      Expanded(
                        child: _buildBMICard(
                          iconAsset: "assets/svg/weight_icon.svg",
                          title: "Weight",
                          value: weight > 0 ? weight.toStringAsFixed(0) : "00",
                          unit: weightUnit,
                        ),
                      ),
                    ],
                  );
                },
              ),
              Space.height(40),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "BMI History",
                    style: TextStyle(
                        fontSize: MySize.size18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Space.height(20),
              AnimatedToggleSwitch<bool>.size(
                current: switchValue,
                values: const [false, true],
                iconOpacity: 0.2,
                inactiveOpacity: 1.0,
                indicatorSize: Size.fromWidth(120),
                customIconBuilder: (context, local, global) => Text(
                  local.value ? 'Monthly' : 'Weekly',
                  style: TextStyle(
                    fontSize: MySize.size15,
                    fontWeight: FontWeight.w700,
                    color: Color.lerp(
                        Theme.of(context).textTheme.bodyMedium!.color!,
                        AppColors.backgroundColor,
                        local.animationValue),
                  ),
                ),
                borderWidth: 1,
                iconAnimationType: AnimationType.onHover,
                style: ToggleStyle(
                  borderColor: Colors.grey,
                  indicatorColor: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(30),
                  indicatorBorderRadius: BorderRadius.circular(20),
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                ),
                selectedIconScale: 1,
                onChanged: (value) => setState(() => switchValue = value),
              ),
              Space.height(30),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size24),
                  color: Theme.of(context).cardColor,
                ),
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 2.4,
                child: Padding(
                  padding: EdgeInsets.all(MySize.size20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Space.height(10),
                      Expanded(
                        child: switchValue
                            ? bmiMonthlyHistory.isNotEmpty
                                ? CustomBmiChart(
                                    data: bmiMonthlyHistory,
                                    isMonthly: switchValue,
                                  )
                                : const Center(child: Text('No data available'))
                            : bmiHistory.isNotEmpty
                                ? CustomBmiChart(
                                    data: bmiHistory,
                                    isMonthly: switchValue,
                                  )
                                : const Center(
                                    child: Text('No data available')),
                      ),
                    ],
                  ),
                ),
              ),
              Space.height(40),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "BMI Categories",
                    style: TextStyle(
                        fontSize: MySize.size18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Space.height(10),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: [
                  BMICategoryCards(
                    title: "Underweight",
                    textColor: AppColors.white,
                    bgColor: AppColors.gauge1,
                    unit: "< 18.5",
                  ),
                  BMICategoryCards(
                    title: "Normal",
                    textColor: AppColors.white,
                    bgColor: AppColors.gauge2,
                    unit: "18.5 - 24.9",
                  ),
                  BMICategoryCards(
                    title: "Overweight",
                    textColor: AppColors.white,
                    bgColor: AppColors.gauge3,
                    unit: "25 - 29.9",
                  ),
                  BMICategoryCards(
                    title: "Obese",
                    textColor: AppColors.white,
                    bgColor: AppColors.gauge4,
                    unit: "> 30",
                  ),
                ],
              ),
              Space.height(40),
              Container(
                padding: EdgeInsets.all(MySize.size15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size10),
                  color: Theme.of(context).cardColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Recommendations",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Space.height(10),
                    Text(
                      'Your Target BMI Range is between 18.5 and 24.5',
                      style: TextStyle(
                          fontSize: MySize.size12, color: AppColors.textGray),
                    ),
                    Space.height(10),
                    Wrap(
                      spacing: 10,
                      runSpacing: 10,
                      children: [
                        BMIRecommendationCards(
                          title: "Maintain Current Weight",
                          value:
                              "Your BMI is in a healthy range. Focus On Maintaining Your Current Weight through Balanced Diet and Regular Excerise",
                          icon: "assets/svg/tick_icon.svg",
                        ),
                        BMIRecommendationCards(
                          title: "Stay Active",
                          value:
                              "Aim for at least 150 Minutes of moderate aerobic activity or 75 Minutes of Vigorous aerobic activity weekly",
                          icon: "assets/svg/heart2_icon.svg",
                        ),
                        BMIRecommendationCards(
                          title: "Healthy Diet",
                          value:
                              "Continue eating a balanced diet rich in fruits, vegetables, whole grains and lean proteins",
                          icon: "assets/svg/apple_icon.svg",
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBMICard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
  }) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.all(MySize.size4),
                  child: SvgPicture.asset(
                    colorFilter: ColorFilter.mode(
                      AppColors.primaryColor,
                      BlendMode.srcIn,
                    ),
                    iconAsset,
                    height: MySize.size24,
                    width: MySize.size24,
                  ),
                ),
                Space.width(8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
              ],
            ),
            Space.height(8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: MySize.size24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.width(4),
                Padding(
                  padding: EdgeInsets.only(bottom: MySize.size4),
                  child: Text(
                    unit,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      color: AppColors.textGray,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
