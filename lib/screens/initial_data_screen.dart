import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/clear_all_controllers.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/utils/validators.dart';
import 'package:healo/common/widgets/custom_button.dart';
import 'package:healo/common/widgets/custom_textformfield.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/users_model.dart';
import 'package:healo/providers/nav_provider.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:healo/providers/auth_state_manager.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:intl/intl.dart';
import 'dart:developer';

class InitialDataScreen extends ConsumerStatefulWidget {
  const InitialDataScreen({super.key});

  @override
  ConsumerState<InitialDataScreen> createState() => _InitialDataScreenState();
}

class _InitialDataScreenState extends ConsumerState<InitialDataScreen> {
  final _nameController = TextEditingController();
  final _dobController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();
  final _genderController = TextEditingController();
  final _formkey = GlobalKey<FormState>();
  final GlobalKey _genderFieldKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  bool _isDropdownOpen = false;
  @override
  void dispose() {
    _nameController.dispose();
    _dobController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    _genderController.dispose();

    super.dispose();
  }

  void clear() {
    clearControllers([
      _nameController,
      _dobController,
      _weightController,
      _heightController,
      _genderController
    ]);
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _removeDropdown();
    } else {
      _showDropdown();
    }
  }

  void _showDropdown() {
    _overlayEntry = _createDropdown();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() => _isDropdownOpen = true);
  }

  void _removeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() => _isDropdownOpen = false);
  }

  OverlayEntry _createDropdown() {
    return OverlayEntry(
      builder: (context) => Positioned(
        width: SizeConfig.screenWidth! - 66, // Match text field width
        child: CompositedTransformFollower(
          link: _layerLink,
          offset: Offset(0, 50), // Position dropdown below the field
          child: Material(
            elevation: MySize.size4,
            borderRadius: Shape.circular(MySize.size10),
            color: Colors.white,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: ["Male", "Female", "Other"].map((String gender) {
                return ListTile(
                  title: Text(gender),
                  onTap: () {
                    setState(() {
                      _genderController.text = gender;
                    });
                    _removeDropdown();
                  },
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Stack(children: [
          Positioned.fill(
              child: SvgPicture.asset(
            "assets/svg/background_image.svg",
            fit: BoxFit.fill,
          )),
          Container(
            height: SizeConfig.screenHeight,
            padding: EdgeInsets.symmetric(horizontal: MySize.size33),
            child: Form(
              key: _formkey,
              child: Column(
                //spacing: 20,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Space.height(80),
                  Text(
                    "Tell us about Yourself",
                    style: TextStyle(
                        fontSize: MySize.size20, fontWeight: FontWeight.w800),
                  ),
                  Space.height(20),
                  CustomTextformfield(
                    label: "Your Name",
                    controller: _nameController,
                    validator: (value) => Validators.validator(value, "Name"),
                  ),
                  Space.height(20),
                  CustomTextformfield(
                    label: "Date Of Birth",
                    controller: _dobController,
                    validator: (value) =>
                        Validators.validator(value, "Date Of Birth"),
                    readOnly: true,
                    suffixIcon: InkWell(
                      onTap: () async {
                        await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime(1950),
                            lastDate: DateTime.now(),
                            builder: (BuildContext context, Widget? child) {
                              return Theme(
                                  data: ThemeData.light().copyWith(
                                    colorScheme: ColorScheme.dark(
                                      primary:
                                          AppColors.primaryColor.withAlpha(200),
                                      // Header and selected day color
                                      onPrimary: Colors.black,
                                      // Header text and selected day text color
                                      surface: AppColors.white,
                                      // Calendar background color
                                      onSurface: AppColors
                                          .black, // Calendar text color
                                    ),
                                  ),
                                  child: child!);
                            }).then((pickedDate) {
                          if (pickedDate != null) {
                            String formattedDate =
                                DateFormat('dd-MM-yyyy').format(pickedDate);

                            _dobController.text = formattedDate;
                          }
                        });
                      },
                      child: Icon(
                        Icons.calendar_month,
                        size: MySize.size22,
                      ),
                    ),
                  ),
                  Space.height(20),
                  CustomTextformfield(
                    controller: _weightController,
                    label: "Weight (in Kgs)",
                    validator: (value) => Validators.validator(value, "Weight"),
                    keyboardType: TextInputType.number,
                  ),
                  Space.height(20),
                  CustomTextformfield(
                    controller: _heightController,
                    label: "Height (in cms)",
                    validator: (value) => Validators.validator(value, "Height"),
                    keyboardType: TextInputType.number,
                  ),
                  Space.height(20),
                  CompositedTransformTarget(
                    link: _layerLink,
                    child: CustomTextformfield(
                      key: _genderFieldKey,
                      label: "Gender",
                      controller: _genderController,
                      readOnly: true,
                      validator: (value) =>
                          Validators.validator(value, "Gender"),
                      suffixIcon: InkWell(
                        onTap: _toggleDropdown,
                        child: Icon(
                          _isDropdownOpen
                              ? Icons.arrow_drop_up
                              : Icons.arrow_drop_down,
                          size: MySize.size24,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                  ),
                  Space.height(20),
                  SizedBox(
                    width: SizeConfig.screenWidth! * .5,
                    child: CustomButton(
                        onTap: () async {
                          if (!_formkey.currentState!.validate()) {
                            return;
                          }
                          double? weight =
                              double.tryParse(_weightController.text);
                          double? height =
                              double.tryParse(_heightController.text);

                          Users newUser = Users(
                            name: _nameController.text,
                            dob: _dobController.text,
                            weight: weight,
                            height: height,
                            gender: _genderController.text,
                            initialDataCompleted: true,
                            // Don't set fcmToken - let it remain null so it's excluded from update
                          );

                          log("Saving user data: ${newUser.toJson()}");
                          // Use safe update method that excludes null values
                          await FirestoreService().updateUserSafely(newUser);

                          // Wait a moment for Firestore to propagate the data
                          await Future.delayed(Duration(milliseconds: 500));

                          // Invalidate user data providers to refresh the data
                          log("Invalidating user data providers");
                          ref.invalidate(userDataProvider);
                          ref.invalidate(userDataStreamProvider);
                          ref.invalidate(userNameProvider);
                          ref.invalidate(userNameStreamProvider);
                          ref.invalidate(userGenderProvider);
                          ref.invalidate(userPhoneProvider);
                          ref.invalidate(userDobProvider);
                          ref.invalidate(userWeightProvider);
                          ref.invalidate(userHeightProvider);
                          ref.invalidate(userInitialDataCompletedProvider);

                          // Force refresh auth state manager
                          await ref.read(authStateManagerProvider.notifier).refreshAllData();

                          clear();
                          if (context.mounted) {
                            // Ensure we're on the home tab (index 0) when navigating to main screen
                            ref.read(bottomNavIndexProvider.notifier).setIndex(0);
                            Navigator.pushNamedAndRemoveUntil(
                                context, mainScreen, (route) => false);
                          }
                        },
                        text: "Next"),
                  ),
                ],
              ),
            ),
          ),
        ]),
      ),
    );
  }
}
