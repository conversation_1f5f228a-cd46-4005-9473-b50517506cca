import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/medication_intake_model.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

/// Provider for managing medication intake state
class MedicationIntakeNotifier extends StateNotifier<List<MedicationIntake>> {
  final FirestoreService _firestoreService;

  MedicationIntakeNotifier(this._firestoreService) : super([]);

  /// Fetch all medication intakes
  Future<void> fetchAllIntakes() async {
    try {
      final intakes = await _firestoreService.getAllMedicationIntakes();
      state = intakes;
    } catch (e) {
      log("Error fetching medication intakes: $e");
    }
  }

  /// Fetch medication intakes for a specific date
  Future<List<MedicationIntake>> fetchIntakesForDate(String date) async {
    try {
      return await _firestoreService.getMedicationIntakeForDate(date);
    } catch (e) {
      log("Error fetching medication intakes for date $date: $e");
      return [];
    }
  }

  /// Record medication as taken
  Future<void> markMedicationTaken(String medicationName, String time, {String? notes}) async {
    try {
      log("Starting to mark medication as taken: $medicationName at $time");
      final now = DateTime.now();
      final date = DateFormat('dd-MM-yyyy').format(now);

      final intake = MedicationIntake(
        medicationName: medicationName,
        date: date,
        time: time,
        status: 'taken',
        timestamp: Timestamp.now(),
        notes: notes,
      );

      log("Recording intake to Firestore: ${intake.toMap()}");
      await _firestoreService.recordMedicationIntake(intake);
      await fetchAllIntakes(); // Refresh the state
      log("Medication marked as taken successfully: $medicationName at $time");
    } catch (e) {
      log("Error marking medication as taken: $e");
      rethrow;
    }
  }

  /// Record medication as skipped
  Future<void> markMedicationSkipped(String medicationName, String time, {String? notes}) async {
    try {
      log("Starting to mark medication as skipped: $medicationName at $time");
      final now = DateTime.now();
      final date = DateFormat('dd-MM-yyyy').format(now);

      final intake = MedicationIntake(
        medicationName: medicationName,
        date: date,
        time: time,
        status: 'skipped',
        timestamp: Timestamp.now(),
        notes: notes,
      );

      log("Recording skipped intake to Firestore: ${intake.toMap()}");
      await _firestoreService.recordMedicationIntake(intake);
      await fetchAllIntakes(); // Refresh the state
      log("Medication marked as skipped successfully: $medicationName at $time");
    } catch (e) {
      log("Error marking medication as skipped: $e");
      rethrow;
    }
  }

  /// Record medication as missed
  Future<void> markMedicationMissed(String medicationName, String time, {String? notes}) async {
    try {
      final now = DateTime.now();
      final date = DateFormat('dd-MM-yyyy').format(now);

      final intake = MedicationIntake(
        medicationName: medicationName,
        date: date,
        time: time,
        status: 'missed',
        timestamp: Timestamp.now(),
        notes: notes,
      );

      await _firestoreService.recordMedicationIntake(intake);
      await fetchAllIntakes(); // Refresh the state
      log("Medication marked as missed: $medicationName at $time");
    } catch (e) {
      log("Error marking medication as missed: $e");
      rethrow;
    }
  }

  /// Update medication intake status
  Future<void> updateIntakeStatus(String medicationName, String date, String time, String newStatus) async {
    try {
      await _firestoreService.updateMedicationIntakeStatus(medicationName, date, time, newStatus);
      await fetchAllIntakes(); // Refresh the state
      log("Medication intake status updated: $medicationName - $newStatus");
    } catch (e) {
      log("Error updating medication intake status: $e");
      rethrow;
    }
  }

  /// Get medication intake statistics for today
  Future<Map<String, int>> getTodayStats() async {
    try {
      final today = DateFormat('dd-MM-yyyy').format(DateTime.now());
      return await _firestoreService.getMedicationIntakeStats(today, today);
    } catch (e) {
      log("Error getting today's medication stats: $e");
      return {'taken': 0, 'skipped': 0, 'missed': 0};
    }
  }

  /// Get medication intake statistics for a date range
  Future<Map<String, int>> getStatsForDateRange(String startDate, String endDate) async {
    try {
      return await _firestoreService.getMedicationIntakeStats(startDate, endDate);
    } catch (e) {
      log("Error getting medication stats for date range: $e");
      return {'taken': 0, 'skipped': 0, 'missed': 0};
    }
  }

  /// Check if medication was taken today at specific time
  bool wasMedicationTakenToday(String medicationName, String time) {
    final today = DateFormat('dd-MM-yyyy').format(DateTime.now());
    return state.any((intake) =>
        intake.medicationName == medicationName &&
        intake.date == today &&
        intake.time == time &&
        intake.status == 'taken');
  }

  /// Check if medication was skipped today at specific time
  bool wasMedicationSkippedToday(String medicationName, String time) {
    final today = DateFormat('dd-MM-yyyy').format(DateTime.now());
    return state.any((intake) =>
        intake.medicationName == medicationName &&
        intake.date == today &&
        intake.time == time &&
        intake.status == 'skipped');
  }

  /// Get intake status for specific medication, date, and time
  String? getIntakeStatus(String medicationName, String date, String time) {
    final intake = state.firstWhere(
      (intake) =>
          intake.medicationName == medicationName &&
          intake.date == date &&
          intake.time == time,
      orElse: () => MedicationIntake(
        medicationName: '',
        date: '',
        time: '',
        status: '',
        timestamp: Timestamp.now(),
      ),
    );

    return intake.medicationName.isNotEmpty ? intake.status : null;
  }
}

/// Provider instance for medication intake management
final medicationIntakeProvider = StateNotifierProvider<MedicationIntakeNotifier, List<MedicationIntake>>(
  (ref) => MedicationIntakeNotifier(FirestoreService()),
);

/// Provider for today's medication intake statistics
final todayMedicationStatsProvider = FutureProvider<Map<String, int>>((ref) async {
  final notifier = ref.read(medicationIntakeProvider.notifier);
  return await notifier.getTodayStats();
});
