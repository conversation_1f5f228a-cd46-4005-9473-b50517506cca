import 'dart:convert';

class WeeklyMedication {
  final String medicineName;
  final String frequency;
  final String dosage;
  final List<String> timing;
  final List<String?> days;
  final String mealTime;
  final String expiryDate;
  final String quantity;
  final String unit;
  final String createdDate; // Format: DD/MM/YYYY

  WeeklyMedication({
    required this.medicineName,
    required this.frequency,
    required this.dosage,
    required this.timing,
    required this.days,
    required this.mealTime,
    required this.expiryDate,
    required this.quantity,
    required this.unit,
    required this.createdDate,
  });

  Map<String, dynamic> toMap() {
    return {
      "medicine_name": medicineName,
      "frequency": frequency,
      "dosage": dosage,
      "timing": timing,
      "days": days,
      "meal_time": mealTime,
      "expiry_date": expiryDate,
      "quantity": quantity,
      "unit": unit,
      "created_date": createdDate,
    };
  }

  factory WeeklyMedication.fromMap(Map<String, dynamic> map) {
    return WeeklyMedication(
      medicineName: map["medicine_name"] ?? "",
      frequency: map["frequency"] ?? "",
      dosage: map["dosage"] ?? "",
      timing: List<String>.from(map["timing"] ?? []),
      days: List<String?>.from(map["days"] ?? []),
      mealTime: map["meal_time"] ?? "",
      expiryDate: map["expiry_date"] ?? "",
      quantity: map["quantity"] ?? "",
      unit: map["unit"] ?? "",
      createdDate: map["created_date"] ?? "",
    );
  }

  String toJson() => json.encode(toMap());
  factory WeeklyMedication.fromJson(String source) =>
      WeeklyMedication.fromMap(json.decode(source));
}
