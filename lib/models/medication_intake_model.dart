import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class MedicationIntake {
  final String medicationName;
  final String date; // Format: DD-MM-YYYY
  final String time; // Format: HH:MM
  final String status; // 'taken', 'skipped', 'missed'
  final Timestamp timestamp;
  final String? notes;

  MedicationIntake({
    required this.medicationName,
    required this.date,
    required this.time,
    required this.status,
    required this.timestamp,
    this.notes,
  });

  Map<String, dynamic> toMap() {
    return {
      'medication_name': medicationName,
      'date': date,
      'time': time,
      'status': status,
      'timestamp': timestamp,
      'notes': notes,
    };
  }

  factory MedicationIntake.fromMap(Map<String, dynamic> map) {
    return MedicationIntake(
      medicationName: map['medication_name'] ?? '',
      date: map['date'] ?? '',
      time: map['time'] ?? '',
      status: map['status'] ?? '',
      timestamp: map['timestamp'] ?? Timestamp.now(),
      notes: map['notes'],
    );
  }

  String toJson() => json.encode(toMap());

  factory MedicationIntake.fromJson(String source) =>
      MedicationIntake.fromMap(json.decode(source));

  MedicationIntake copyWith({
    String? medicationName,
    String? date,
    String? time,
    String? status,
    Timestamp? timestamp,
    String? notes,
  }) {
    return MedicationIntake(
      medicationName: medicationName ?? this.medicationName,
      date: date ?? this.date,
      time: time ?? this.time,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      notes: notes ?? this.notes,
    );
  }
}
