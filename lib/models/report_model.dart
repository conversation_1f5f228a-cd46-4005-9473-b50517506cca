import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:healo/models/report_analysis_model.dart';
import 'package:intl/intl.dart';

class Report {
  String id;
  final String name;
  final String url;
  final DateTime uploadedAt;
  final String? localPath; // Local file path (optional)
  final ReportAnalysis? analysis; // AI analysis results

  Report({
    required this.id,
    required this.name,
    required this.url,
    required this.uploadedAt,
    this.localPath, // Optional field
    this.analysis, // Optional field for AI analysis
  });

  /// Get formatted date from uploadedAt (e.g., "Dec 15, 2023")
  String get uploadedAtDate {
    return DateFormat('MMM dd, yyyy').format(uploadedAt);
  }

  /// Get formatted time from uploadedAt (e.g., "2:30 PM")
  String get uploadedAtTime {
    return DateFormat('h:mm a').format(uploadedAt);
  }

  factory Report.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Report(
      id: doc.id,
      name: data['name'] ?? '',
      url: data['url'] ?? '',
      uploadedAt: (data['uploadedAt'] as Timestamp).toDate(),
      localPath: data['localPath'], // Optional field
      analysis: data['analysis'] != null
          ? ReportAnalysis.fromMap(doc.id, data['analysis'])
          : null,
    );
  }

  factory Report.fromMap(String reportId, Map<String, dynamic> data) {
    return Report(
      id: reportId,
      name: data['name'] ?? '',
      url: data['url'] ?? '',
      uploadedAt: (data['uploadedAt'] as Timestamp).toDate(),
      localPath: data['localPath'], // Optional field
      analysis: data['analysis'] != null
          ? ReportAnalysis.fromMap(reportId, data['analysis'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'url': url,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'localPath': localPath,
      'analysis': analysis?.toMap(),
    };
  }
}
