import 'dart:convert';

class DailyMedication {
  final String medicineName;
  final String frequency;
  final String dosage;
  final List<String> timing;
  final String mealTime;
  final String expiryDate;
  final String quantity;
  final String unit;
  final String createdDate; // Format: DD/MM/YYYY

  DailyMedication({
    required this.medicineName,
    required this.frequency,
    required this.dosage,
    required this.timing,
    required this.mealTime,
    required this.expiryDate,
    required this.quantity,
    required this.unit,
    required this.createdDate,
  });

  Map<String, dynamic> toMap() {
    return {
      "medicine_name": medicineName,
      "frequency": frequency,
      "dosage": dosage,
      "timing": timing,
      "meal_time": mealTime,
      "expiry_date": expiryDate,
      "quantity": quantity,
      "unit": unit,
      "created_date": createdDate,
    };
  }

  factory DailyMedication.fromMap(Map<String, dynamic> map) {
    return DailyMedication(
      medicineName: map["medicine_name"] ?? "",
      frequency: map["frequency"] ?? "",
      dosage: map["dosage"] ?? "",
      timing: List<String>.from(map["timing"] ?? []),
      mealTime: map["meal_time"] ?? "",
      expiryDate: map["expiry_date"] ?? "",
      quantity: map["quantity"] ?? "",
      unit: map["unit"] ?? "",
      createdDate: map["created_date"] ?? "",
    );
  }

  String toJson() => json.encode(toMap());
  factory DailyMedication.fromJson(String source) =>
      DailyMedication.fromMap(json.decode(source));
}
