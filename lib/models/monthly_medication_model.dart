import 'dart:convert';

class MonthlyMedication {
  final String medicineName;
  final String frequency;
  final String dosage;
  final List<String> timing;
  final List<String?> dates;
  final String mealTime;
  final String expiryDate;
  final String quantity;
  final String unit;
  final String createdDate; // Format: DD/MM/YYYY

  MonthlyMedication({
    required this.medicineName,
    required this.frequency,
    required this.dosage,
    required this.timing,
    required this.dates,
    required this.mealTime,
    required this.expiryDate,
    required this.quantity,
    required this.unit,
    required this.createdDate,
  });

  Map<String, dynamic> toMap() {
    return {
      "medicine_name": medicineName,
      "frequency": frequency,
      "dosage": dosage,
      "timing": timing,
      "dates": dates,
      "meal_time": mealTime,
      "expiry_date": expiryDate,
      "quantity": quantity,
      "unit": unit,
      "created_date": createdDate,
    };
  }

  factory MonthlyMedication.fromMap(Map<String, dynamic> map) {
    return MonthlyMedication(
      medicineName: map["medicine_name"] ?? "",
      frequency: map["frequency"] ?? "",
      dosage: map["dosage"] ?? "",
      timing: List<String>.from(map["timing"] ?? []),
      dates: List<String?>.from(map["dates"] ?? []),
      mealTime: map["meal_time"] ?? "",
      expiryDate: map["expiry_date"] ?? "",
      quantity: map["quantity"] ?? "",
      unit: map["unit"] ?? "",
      createdDate: map["created_date"] ?? "",
    );
  }

  String toJson() => json.encode(toMap());
  factory MonthlyMedication.fromJson(String source) =>
      MonthlyMedication.fromMap(json.decode(source));
}
